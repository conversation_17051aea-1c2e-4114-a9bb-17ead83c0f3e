const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const exclusionList = require('metro-config/src/defaults/exclusionList');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
    watchFolders: [], // متراقبش فولدرات زيادة

    resolver: {
        blacklistRE: exclusionList([
            /node_modules\/.*\/node_modules\/react-native\/.*/, // منع التداخل بين نسخ React Native
        ]),
    },

    server: {
        // تقليل الـ polling لو تحب، لكن ده اختياري
        enhanceMiddleware: (middleware) => {
            return (req, res, next) => {
                res.setHeader('Connection', 'keep-alive');
                return middleware(req, res, next);
            };
        },
    },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
