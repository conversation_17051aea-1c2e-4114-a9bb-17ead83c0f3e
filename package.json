{"name": "ModrkClient", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "react": "18.2.0", "react-native": "0.74.1", "react-native-gesture-handler": "^2.27.1", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-thunk": "^3.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.83", "@react-native/eslint-config": "0.74.83", "@react-native/metro-config": "0.74.83", "@react-native/typescript-config": "0.74.83", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}